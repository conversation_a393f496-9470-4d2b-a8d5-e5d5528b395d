# ===================================
# AI Assistant & Copilot Instructions
# ===================================
# IMPORTANT: Keep these files for AI assistant functionality
.github/copilot-instructions.md

# ===================================
# Dependencies & Package Managers
# ===================================
node_modules/
.pnp
.pnp.js
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Package manager lock files (keep pnpm-lock.yaml as it's committed)
package-lock.json
yarn.lock

# ===================================
# Build Outputs & Artifacts
# ===================================
dist/
build/
out/
*.tsbuildinfo

# ===================================
# Environment Variables
# ===================================
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# ===================================
# Logs & Debug Files
# ===================================
logs/
*.log
npm-debug.log*
yarn-debug.log*
pnpm-debug.log*
lerna-debug.log*

# ===================================
# Runtime & Process Files
# ===================================
pids/
*.pid
*.seed
*.pid.lock

# ===================================
# Testing & Coverage
# ===================================
coverage/
*.lcov
.nyc_output/
.jest/
test-results/
playwright-report/

# ===================================
# Cache Directories
# ===================================
.cache/
.parcel-cache/
.npm/
.eslintcache
.swc/
.turbo/

# ===================================
# TypeScript & Build Tools
# ===================================
*.tsbuildinfo
.tsc-cache/
.tsup/

# ===================================
# gRPC & Protocol Buffers
# ===================================
# Generated proto files are included in git for this project
# But exclude any accidental proto compilation artifacts
*.pb.go
*.pb.ts
*.pb.js
*.pb.d.ts
*.grpc
*.descriptor
*.desc

# ===================================
# IDE & Editor Files
# ===================================
# VS Code
.vscode/launch.json
.vscode/tasks.json
.vscode/c_cpp_properties.json
.vscode/*.code-workspace
.vscode-test/

# IntelliJ IDEA
.idea/
*.iml
*.ipr
*.iws

# Vim/Neovim
*.swp
*.swo
*~
.vim/
.nvim/

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Sublime Text
*.sublime-workspace
*.sublime-project

# ===================================
# Operating System Files
# ===================================
# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
.AppleDouble
.LSOverride

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ===================================
# Temporary Files
# ===================================
*.tmp
*.temp
*.bak
*.backup
*.orig
*.rej

# ===================================
# Archive Files
# ===================================
*.tgz
*.tar.gz
*.zip
*.rar
*.7z

# ===================================
# Optional Files
# ===================================
.node_repl_history
.tern-port
